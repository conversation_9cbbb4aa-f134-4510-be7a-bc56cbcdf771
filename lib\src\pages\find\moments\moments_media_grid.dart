// lib/src/pages/find/moments/moments_media_grid.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';

// 在moments_media_grid.dart中添加以下代码

// 媒体类型枚举
enum MediaType {
  image,
  video
}

// 媒体项模型
class MediaItem {
  final String url;
  final MediaType type;
  final String? thumbnailUrl; // 视频缩略图URL
  final String? duration; // 视频时长

  MediaItem({
    required this.url,
    required this.type,
    this.thumbnailUrl,
    this.duration,
  });
}

class MomentsMediaGrid extends StatelessWidget {
  final List<Map<String, Object>> mediaItems;
  final double spacing = 5.0;
  
  const MomentsMediaGrid({
    Key? key,
    required this.mediaItems,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (mediaItems.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // 单张图片显示
    if (mediaItems.length == 1) {
      return _buildSingleImage(context);
    }
    
    // 多张图片网格显示
    return _buildImageGrid(context);
  }
  
  // 判断是否为视频文件
  bool _isVideo(Map<String, Object> mediaItem) {
    return mediaItem['mediaType'].toString() == 'video';
  }

  // 构建单张媒体视图
  Widget _buildSingleImage(BuildContext context) {
    final mediaItem = mediaItems[0];
    final isVideo = _isVideo(mediaItem);

    return GestureDetector(
      onTap: () => _previewImages(context, 0),
      child: Container(
        width: 128,
        height: 188,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              if (isVideo)
                // 视频缩略图
                Container(
                  color: Colors.black,
                  child: const Center(
                    child: Icon(
                      Icons.play_circle_outline,
                      color: Colors.white,
                      size: 48,
                    ),
                  ),
                )
              else
                // 图片
                CachedNetworkImage(
                  imageUrl: mediaItem['mediaUrl'].toString(),
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const SizedBox(
                    width: 128,
                    height: 188,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                ),
              // 视频标识
              if (isVideo)
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.videocam,
                          color: Colors.white,
                          size: 12,
                        ),
                        SizedBox(width: 2),
                        Text(
                          '视频',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  // 构建图片网格
  Widget _buildImageGrid(BuildContext context) {
    // 确定网格布局
    int crossAxisCount = _getCrossAxisCount();
    
    return Container(
      constraints: const BoxConstraints(
        maxWidth: 300,
      ),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: spacing,
          crossAxisSpacing: spacing,
        ),
        itemCount: mediaItems.length > 9 ? 9 : mediaItems.length,
        itemBuilder: (context, index) {
          // 如果是第9张且总数超过9张，显示+N
          if (index == 8 && mediaItems.length > 9) {
            return _buildMoreIndicator(context, mediaItems.length - 9);
          }
          
          final mediaItem = mediaItems[index];
          final isVideo = _isVideo(mediaItem);

          return GestureDetector(
            onTap: () => _previewImages(context, index),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  if (isVideo)
                    // 视频缩略图
                    Container(
                      color: Colors.black,
                      child: const Center(
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    )
                  else
                    // 图片
                    CachedNetworkImage(
                      imageUrl: mediaItem['mediaUrl'].toString(),
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  // 视频标识
                  if (isVideo)
                    Positioned(
                      bottom: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: const Icon(
                          Icons.videocam,
                          color: Colors.white,
                          size: 10,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  // 构建"更多"指示器
  Widget _buildMoreIndicator(BuildContext context, int moreCount) {
    return GestureDetector(
      onTap: () => _previewImages(context, 8),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black45,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            '+$moreCount',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
  
  // 根据图片数量确定网格列数
  int _getCrossAxisCount() {
    if (mediaItems.length <= 4) {
      return 2; // 2-4张图片显示2列
    }
    return 3; // 5-9张图片显示3列
  }
  
  // 预览媒体
void _previewImages(BuildContext context, int initialIndex) {
  // 使用Navigator.push创建一个全屏页面
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => MediaPreviewPage(
        mediaItems: mediaItems,
        initialIndex: initialIndex,
      ),
    ),
  );
}
}

// 媒体预览页面
class MediaPreviewPage extends StatefulWidget {
  final List<Map<String, Object>> mediaItems;
  final int initialIndex;

  const MediaPreviewPage({
    Key? key,
    required this.mediaItems,
    required this.initialIndex,
  }) : super(key: key);

  @override
  _MediaPreviewPageState createState() => _MediaPreviewPageState();
}

class _MediaPreviewPageState extends State<MediaPreviewPage> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  bool _isVideo(Map<String, Object> mediaItem) {
    return mediaItem['mediaType'].toString() == 'video';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white, size: 30),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.mediaItems.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          final mediaItem = widget.mediaItems[index];
          final isVideo = _isVideo(mediaItem);

          if (isVideo) {
            return VideoPlayerWidget(
              videoUrl: mediaItem['mediaUrl'].toString(),
            );
          } else {
            return InteractiveViewer(
              minScale: 0.5,
              maxScale: 3.0,
              child: Center(
                child: Image.network(
                  mediaItem['mediaUrl'].toString(),
                  fit: BoxFit.contain,
                ),
              ),
            );
          }
        },
      ),
    );
  }
}

// 视频播放器组件
class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerWidget({
    Key? key,
    required this.videoUrl,
  }) : super(key: key);

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  void _initializeVideo() {
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
    _controller.initialize().then((_) {
      setState(() {
        _isInitialized = true;
      });
    }).catchError((error) {
      print('视频初始化失败: $error');
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _togglePlayPause() {
    setState(() {
      if (_controller.value.isPlaying) {
        _controller.pause();
      } else {
        _controller.play();
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return GestureDetector(
      onTap: _toggleControls,
      child: Center(
        child: AspectRatio(
          aspectRatio: _controller.value.aspectRatio,
          child: Stack(
            alignment: Alignment.center,
            children: [
              VideoPlayer(_controller),
              if (_showControls)
                Container(
                  color: Colors.black26,
                  child: Center(
                    child: IconButton(
                      icon: Icon(
                        _controller.value.isPlaying
                            ? Icons.pause_circle_outline
                            : Icons.play_circle_outline,
                        color: Colors.white,
                        size: 64,
                      ),
                      onPressed: _togglePlayPause,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}